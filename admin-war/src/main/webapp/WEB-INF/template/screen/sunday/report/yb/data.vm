#parse("/screen/sunday/operate.vm")
<table width="100%"  border="0" cellspacing="0" cellpadding="0" >
    <tr>
        <th width="5%">序号</th>
        <th width="25%">月份</th>
        <th width="15%">状态</th>
        <th width="20%">创建时间</th>
        <th>操作</th>
    </tr>
    #foreach($!zs in $!zbs)
        <tr>
            <td>$!velocityCount</td>
            <td>$!zs.month 月</td>
            <td>
            	#if($!zs.status == 0) 待发布 #end
            	#if($!zs.status == 1) 已发布 #end
            </td>
            <td>$!zs.createTime</td>
            <td>
                <button type="button" onclick="newTab('/sunday/web/yb/input?code=$!zs.code&id=$!zs.id',null);" class="sunday_food_edit">#if($!zs.status == 1 || $!zs.status == 4) 查看#else 编辑 #end </button>
                <button #if($!zs.status == 1) style="background: #DCDCDC" disabled #end type="button" onclick="sunday_report_delete($!zs.id);" class="button-delete">删除</button>
            	<button #if($!zs.status == 1) style="background: #DCDCDC" disabled #end type="button" onclick="sunday_report_publish($!zs.id);" class="sunday_food_edit">发布</button>
            </td>
        </tr>
    #end
</table>
<div class="com-page-wrapper">
    <span class="page-num">共$!total条，$!currentPage/$!totalPage页</span>
    <!--判断是否能够转跳页数-->
        <span class="next pre #if($!prevPage >= $!minPage) on" onclick=sunday_report_search_page($!prevPage,null) #end"></span>
        <span class="next #if($!maxPage >= $!nextPage) on" onclick=sunday_report_search_page($!nextPage,null) #end"></span>
    <span class="text">跳转到：</span>
    <input type="text" value="$!currentPage" min="$!minPage" max="$!maxPage">
    <span class="go" onclick="sunday_report_search_page($(this).prev().val(),null,$!maxPage)">GO</span>
</div>