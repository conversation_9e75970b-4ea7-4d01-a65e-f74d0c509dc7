package org.itboys.mobile.entity.mysql.report;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;

import org.itboys.mongodb.entity.BaseMySqlEntity;

/**
 * 月报内容
 *
 * <AUTHOR>
 */
public class SundayReportYb extends BaseMySqlEntity {

    private Long id;            //
    //课程编码
    private String code;
    //课程名称
    private String course;
    //年份
    private Integer year;
    //月份
    private Integer month;
    //报告日期
    private String date;
    //教学单元
    private String theme;
    //本月主题
    private String themeOfMonth;

    //本月主题
    private String[] themeArray;

    //成长记录[数组]
    private String growthRecord;
    private String[] growthRecordArray;
    //教师评语[数组]
    private String teacherComment;
    private String[] teacherCommentArray;
    //状态：0=待发布；1=已发布
    private Integer status;
    //是否存在：1=存在
    private Integer isExists;

    private String weeksInMonth;
    //开始日期
    private String startDate;
    //结束日期
    private String endDate;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getCourse() {
        return course;
    }

    public void setCourse(String course) {
        this.course = course;
    }

    public Integer getYear() {
        return year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public Integer getMonth() {
        return month;
    }

    public void setMonth(Integer month) {
        this.month = month;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public String getTheme() {
        return theme;
    }

    public void setTheme(String theme) {
        this.theme = theme;
    }

    public String getGrowthRecord() {
        return growthRecord;
    }

    public void setGrowthRecord(String growthRecord) {
        this.growthRecord = growthRecord;
    }

    public String getTeacherComment() {
        return teacherComment;
    }

    public void setTeacherComment(String teacherComment) {
        this.teacherComment = teacherComment;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String[] getGrowthRecordArray() {
        return growthRecordArray;
    }

    public void setGrowthRecordArray(String[] growthRecordArray) {
        this.growthRecordArray = growthRecordArray;
    }

    public String[] getTeacherCommentArray() {
        return teacherCommentArray;
    }

    public void setTeacherCommentArray(String[] teacherCommentArray) {
        this.teacherCommentArray = teacherCommentArray;
    }

    public Integer getIsExists() {
        return isExists;
    }

    public void setIsExists(Integer isExists) {
        this.isExists = isExists;
    }

    public String getWeeksInMonth() {
        if (this.date != null) {
            try {
                SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(df.parse(date));
                //设置每周第一天为周一 默认每周第一天为周日
                calendar.setFirstDayOfWeek(Calendar.MONDAY);
                //获取当前日期所在周周日
                calendar.set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY);
                return String.valueOf((String.valueOf(calendar.get(Calendar.MONTH) + 1)).concat("月第").
                        concat(String.valueOf(calendar.get(Calendar.WEEK_OF_MONTH))).concat("周"));
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    public void setWeeksInMonth(String weeksInMonth) {
        this.weeksInMonth = weeksInMonth;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

}

